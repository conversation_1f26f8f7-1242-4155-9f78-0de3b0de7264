package com.iflytek.cdc.admin.controller.brief;

import com.iflytek.cdc.admin.dto.brief.BriefReportStatsDto;
import com.iflytek.cdc.admin.service.brief.BriefReportStatsService;
import com.iflytek.cdc.admin.vo.brief.BriefReportStatsVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "报告统计")
@RestController
@RequestMapping("/pt/{version}/brief/stats")
@RequiredArgsConstructor
public class BriefReportStatsController {

    private BriefReportStatsService briefReportStatsService;

    @Autowired
    public void setBriefReportStatsService(BriefReportStatsService briefReportStatsService) {
        this.briefReportStatsService = briefReportStatsService;
    }

    @ApiOperation("报告推送统计")
    @PostMapping("/reportPushStats")
    public List<BriefReportStatsVo> reportPushStats(@RequestBody BriefReportStatsDto dto){
        return briefReportStatsService.reportPushStats(dto);
    }
}
