package com.iflytek.cdc.admin.service.brief;

import com.iflytek.cdc.admin.dto.brief.BriefReportStatsDto;
import com.iflytek.cdc.admin.mapper.brief.PushRecordMapper;
import com.iflytek.cdc.admin.vo.brief.BriefReportStatsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class BriefReportStatsServiceImpl implements BriefReportStatsService{


    private PushRecordMapper pushRecordMapper;

    @Autowired
    public void setPushRecordMapper(PushRecordMapper pushRecordMapper) {
        this.pushRecordMapper = pushRecordMapper;
    }

    @Override
    public List<BriefReportStatsVo> reportPushStats(BriefReportStatsDto dto) {
        List<BriefReportStatsVo> list = pushRecordMapper.reportPushStats(dto);
        
        return Collections.emptyList();
    }
}
